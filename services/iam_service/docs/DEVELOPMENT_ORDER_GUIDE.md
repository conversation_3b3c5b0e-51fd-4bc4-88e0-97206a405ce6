# IAM服务开发顺序指南

## 📋 项目概述

IAM（Identity and Access Management）服务是一个基于RBAC的企业级用户权限管理系统，采用FastAPI + React的前后端分离架构。本文档详细说明前端和后端的开发顺序，确保开发过程有序进行。

## 🏗️ 整体架构

### 技术栈
- **后端**: FastAPI + SQLAlchemy + PostgreSQL + Redis
- **前端**: React 18 + TypeScript + Vite + Tailwind CSS
- **认证**: JWT + 会话管理
- **权限**: RBAC (Role-Based Access Control)

### 核心模块
1. **租户管理** - 多租户支持
2. **用户管理** - 用户生命周期管理
3. **认证安全** - 登录、MFA、会话管理
4. **角色权限** - RBAC权限控制
5. **审计日志** - 操作审计追踪
6. **系统配置** - 系统参数管理
7. **高级安全** - 威胁检测、安全策略

## 🔧 后端开发顺序

### 第一阶段：基础设施搭建 (1-2周)

#### 1.1 项目初始化 ✅
- [x] 项目结构搭建
- [x] 依赖管理配置 (requirements.txt)
- [x] 数据库连接配置 (PostgreSQL + Redis)
- [x] 依赖注入容器设置 (dependency-injector)
- [x] 基础配置管理

#### 1.2 核心基础设施 ✅
- [x] 数据库模型定义 (SQLAlchemy)
- [x] 基础中间件 (认证、CORS、异常处理)
- [x] 统一响应格式
- [x] 日志系统配置
- [x] 缓存管理器 (Redis)

#### 1.3 安全基础设施 ✅
- [x] JWT令牌管理
- [x] 密码加密工具 (bcrypt)
- [x] 会话管理器
- [x] 安全工具类 (TOTP、设备指纹)

### 第二阶段：核心业务模块 (3-4周)

#### 2.1 租户管理模块 ✅
**优先级**: 🔴 最高 (其他模块依赖)

**开发顺序**:
1. 租户数据模型和服务层
2. 租户CRUD接口实现
3. 租户状态管理
4. 租户配置管理
5. 租户统计功能

**关键接口**:
- `POST /api/v1/tenants/create` - 创建租户
- `POST /api/v1/tenants/query` - 查询租户列表
- `POST /api/v1/tenants/detail` - 获取租户详情
- `POST /api/v1/tenants/update` - 更新租户信息
- `POST /api/v1/tenants/delete` - 删除租户

#### 2.2 用户管理模块 ✅
**优先级**: 🔴 最高 (认证基础)

**开发顺序**:
1. 用户数据模型和基础服务
2. 用户注册和创建功能
3. 用户信息管理 (CRUD)
4. 用户状态管理 (激活、禁用、锁定)
5. 用户查询和统计功能

**关键接口**:
- `POST /api/v1/users/create` - 创建用户
- `POST /api/v1/users/register` - 用户注册
- `POST /api/v1/users/query` - 查询用户列表
- `POST /api/v1/users/detail` - 获取用户详情
- `POST /api/v1/users/update` - 更新用户信息
- `POST /api/v1/users/activate` - 激活用户

#### 2.3 认证安全模块 ✅
**优先级**: 🔴 最高 (系统入口)

**开发顺序**:
1. 基础登录认证功能
2. JWT令牌生成和验证
3. 会话管理功能
4. 密码管理 (修改、重置)
5. 验证码系统 (短信、邮箱)
6. 多因子认证 (MFA)
7. 设备管理和安全检查

**关键接口**:
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/logout` - 用户登出
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/change-password` - 修改密码
- `POST /api/v1/auth/send-verification` - 发送验证码
- `POST /api/v1/auth/sessions/query` - 查询会话

### 第三阶段：权限管理模块 (2-3周)

#### 3.1 角色管理模块 ✅
**优先级**: 🟡 高 (权限基础)

**开发顺序**:
1. 角色数据模型和基础服务
2. 角色CRUD操作
3. 角色层级和继承关系
4. 角色权限分配
5. 角色统计和查询

**关键接口**:
- `POST /api/v1/roles/create` - 创建角色
- `POST /api/v1/roles/query` - 查询角色列表
- `POST /api/v1/roles/detail` - 获取角色详情
- `POST /api/v1/roles/update` - 更新角色
- `POST /api/v1/roles/delete` - 删除角色

#### 3.2 权限管理模块 ✅
**优先级**: 🟡 高 (权限控制)

**开发顺序**:
1. 权限数据模型和服务
2. 权限CRUD操作
3. 权限分类和组织
4. 权限分配和回收
5. 权限检查和验证

**关键接口**:
- `POST /api/v1/permissions/create` - 创建权限
- `POST /api/v1/permissions/query` - 查询权限列表
- `POST /api/v1/permissions/assign` - 分配权限
- `POST /api/v1/permissions/check` - 权限检查

#### 3.3 RBAC权限管理 ✅
**优先级**: 🟡 高 (权限核心)

**开发顺序**:
1. RBAC核心逻辑实现
2. 用户角色分配管理
3. 角色权限分配管理
4. 权限继承和计算
5. 权限缓存优化

**关键接口**:
- `POST /api/v1/rbac/users/roles/assign` - 分配用户角色
- `POST /api/v1/rbac/roles/permissions/assign` - 分配角色权限
- `POST /api/v1/rbac/permissions/check` - 权限检查
- `POST /api/v1/rbac/users/permissions` - 查询用户权限

### 第四阶段：高级功能模块 (2-3周)

#### 4.1 审计日志系统 ✅
**优先级**: 🟢 中 (合规要求)

**开发顺序**:
1. 审计日志数据模型
2. 自动日志记录机制
3. 日志查询和过滤
4. 日志统计分析
5. 日志导出功能

**关键接口**:
- `POST /api/v1/audit/logs/query` - 查询审计日志
- `POST /api/v1/audit/logs/statistics` - 日志统计
- `POST /api/v1/audit/logs/export` - 导出日志

#### 4.2 系统配置模块 ✅
**优先级**: 🟢 中 (系统管理)

**开发顺序**:
1. 配置数据模型和服务
2. 配置CRUD操作
3. 配置继承机制
4. 敏感配置加密
5. 批量配置操作

**关键接口**:
- `POST /api/v1/system/config/set` - 设置配置
- `POST /api/v1/system/config/get` - 获取配置
- `POST /api/v1/system/config/query` - 查询配置列表
- `POST /api/v1/system/config/batch-set` - 批量设置

#### 4.3 高级安全功能 ✅
**优先级**: 🟢 中 (安全增强)

**开发顺序**:
1. 多因子认证 (MFA) 完善
2. 安全策略配置
3. 威胁检测机制
4. 安全事件管理
5. 风险评估系统

**关键接口**:
- `POST /api/v1/security/mfa/setup` - 设置MFA
- `POST /api/v1/security/policy/set` - 设置安全策略
- `POST /api/v1/security/events/query` - 查询安全事件

### 第五阶段：扩展功能模块 (1-2周)

#### 5.1 知识库管理 ✅
**优先级**: 🔵 低 (业务扩展)

#### 5.2 文档管理 ✅
**优先级**: 🔵 低 (业务扩展)

#### 5.3 外部服务集成 ✅
**优先级**: 🟢 中 (功能完善)
- 邮件服务 (SMTP)
- 短信服务 (阿里云、腾讯云)
- 验证码服务

### 第六阶段：系统监控和优化 (1周)

#### 6.1 系统监控 ✅
- 健康检查接口
- 性能监控
- 资源使用统计
- 错误日志收集

#### 6.2 后台任务系统 ✅
- 清理任务 (过期会话、验证码)
- 通知任务 (异步邮件、短信)
- 用户任务 (数据同步、统计)

## 🎨 前端开发顺序

### 第一阶段：项目基础搭建 (1周)

#### 1.1 项目初始化 ✅
- [x] React + TypeScript + Vite 项目搭建
- [x] Tailwind CSS 样式系统配置
- [x] 路由系统配置 (React Router v6)
- [x] 状态管理配置 (Zustand)
- [x] API 客户端配置 (Axios)

#### 1.2 基础组件库 ✅
- [x] UI 组件系统 (Button, Input, Card, Modal)
- [x] 布局组件 (Header, Sidebar, Layout)
- [x] 通用组件 (ErrorBoundary, Loading)
- [x] 表单组件和验证

#### 1.3 开发工具配置 ✅
- [x] ESLint + Prettier 代码规范
- [x] Vitest 测试框架
- [x] Storybook 组件文档
- [x] Mock 数据服务 (MSW)

### 第二阶段：认证和基础页面 (1-2周)

#### 2.1 认证模块 🔴 最高优先级
**开发顺序**:
1. 登录页面
2. 注册页面
3. 密码重置页面
4. MFA 设置页面
5. 会话管理页面

**页面组件**:
- `LoginPage` - 用户登录
- `RegisterPage` - 用户注册
- `ForgotPasswordPage` - 忘记密码
- `ResetPasswordPage` - 重置密码
- `MFASetupPage` - MFA 设置
- `SessionManagementPage` - 会话管理

#### 2.2 主框架页面
**开发顺序**:
1. 主布局组件
2. 导航菜单
3. 用户信息展示
4. 权限控制组件
5. 错误页面 (404, 403, 500)

**核心组件**:
- `MainLayout` - 主布局
- `NavigationMenu` - 导航菜单
- `UserProfile` - 用户信息
- `PermissionGuard` - 权限守卫
- `ErrorPages` - 错误页面

### 第三阶段：核心业务页面 (2-3周)

#### 3.1 用户管理模块 🔴 最高优先级
**开发顺序**:
1. 用户列表页面
2. 用户详情页面
3. 用户创建/编辑页面
4. 用户角色管理页面
5. 用户权限查看页面

**页面组件**:
- `UserListPage` - 用户列表
- `UserDetailPage` - 用户详情
- `UserCreatePage` - 创建用户
- `UserEditPage` - 编辑用户
- `UserRolesPage` - 用户角色管理
- `UserPermissionsPage` - 用户权限查看

#### 3.2 角色管理模块 🟡 高优先级
**开发顺序**:
1. 角色列表页面
2. 角色详情页面
3. 角色创建/编辑页面
4. 角色权限分配页面
5. 角色用户管理页面

**页面组件**:
- `RoleListPage` - 角色列表
- `RoleDetailPage` - 角色详情
- `RoleCreatePage` - 创建角色
- `RoleEditPage` - 编辑角色
- `RolePermissionsPage` - 角色权限分配
- `RoleUsersPage` - 角色用户管理

#### 3.3 权限管理模块 🟡 高优先级
**开发顺序**:
1. 权限列表页面
2. 权限详情页面
3. 权限创建/编辑页面
4. 权限分配页面
5. 权限树形展示

**页面组件**:
- `PermissionListPage` - 权限列表
- `PermissionDetailPage` - 权限详情
- `PermissionCreatePage` - 创建权限
- `PermissionEditPage` - 编辑权限
- `PermissionTreePage` - 权限树形展示

### 第四阶段：管理功能页面 (2周)

#### 4.1 租户管理模块 🟡 高优先级
**开发顺序**:
1. 租户列表页面
2. 租户详情页面
3. 租户创建/编辑页面
4. 租户配置管理页面
5. 租户统计页面

**页面组件**:
- `TenantListPage` - 租户列表
- `TenantDetailPage` - 租户详情
- `TenantCreatePage` - 创建租户
- `TenantEditPage` - 编辑租户
- `TenantConfigPage` - 租户配置
- `TenantStatsPage` - 租户统计

#### 4.2 审计日志模块 🟢 中优先级
**开发顺序**:
1. 审计日志列表页面
2. 日志详情页面
3. 日志搜索和过滤
4. 日志统计图表
5. 日志导出功能

**页面组件**:
- `AuditLogListPage` - 审计日志列表
- `AuditLogDetailPage` - 日志详情
- `AuditLogSearchPage` - 日志搜索
- `AuditLogStatsPage` - 日志统计
- `AuditLogExportPage` - 日志导出

### 第五阶段：高级功能页面 (1-2周)

#### 5.1 系统配置模块 🟢 中优先级
**开发顺序**:
1. 系统配置列表页面
2. 配置编辑页面
3. 配置分类管理
4. 配置导入导出
5. 配置历史记录

**页面组件**:
- `SystemConfigListPage` - 系统配置列表
- `SystemConfigEditPage` - 配置编辑
- `ConfigCategoryPage` - 配置分类
- `ConfigImportExportPage` - 配置导入导出
- `ConfigHistoryPage` - 配置历史

#### 5.2 安全管理模块 🟢 中优先级
**开发顺序**:
1. 安全策略配置页面
2. 安全事件列表页面
3. 威胁检测页面
4. 安全报告页面
5. 安全设置页面

**页面组件**:
- `SecurityPolicyPage` - 安全策略
- `SecurityEventListPage` - 安全事件
- `ThreatDetectionPage` - 威胁检测
- `SecurityReportPage` - 安全报告
- `SecuritySettingsPage` - 安全设置

### 第六阶段：仪表板和统计 (1周)

#### 6.1 仪表板模块 🔵 低优先级
**开发顺序**:
1. 主仪表板页面
2. 用户统计图表
3. 系统监控面板
4. 安全状态展示
5. 快速操作面板

**页面组件**:
- `DashboardPage` - 主仪表板
- `UserStatsChart` - 用户统计图表
- `SystemMonitorPanel` - 系统监控
- `SecurityStatusPanel` - 安全状态
- `QuickActionsPanel` - 快速操作

## 🔄 开发协调和集成

### 前后端协调开发

#### 1. API 接口对接
- 后端优先完成核心接口
- 前端使用 Mock 数据并行开发
- 逐步替换 Mock 为真实接口
- 接口文档实时同步更新

#### 2. 数据格式统一
- 统一请求响应格式
- 错误码和消息规范
- 分页参数标准化
- 时间格式统一处理

#### 3. 权限控制集成
- 后端权限验证优先
- 前端权限展示跟进
- 路由级权限控制
- 组件级权限隐藏

### 测试和质量保证

#### 1. 单元测试
- 后端服务层测试覆盖
- 前端组件测试覆盖
- 工具函数测试覆盖
- 关键业务逻辑测试

#### 2. 集成测试
- API 接口集成测试
- 前后端联调测试
- 权限流程端到端测试
- 用户场景测试

#### 3. 性能测试
- 接口响应时间测试
- 前端页面加载测试
- 数据库查询优化
- 缓存效果验证

## 📅 开发时间规划

### 总体时间安排 (8-10周)

#### 后端开发 (5-6周)
- 第1-2周: 基础设施搭建
- 第3-4周: 核心业务模块
- 第5周: 权限管理模块
- 第6周: 高级功能和优化

#### 前端开发 (4-5周)
- 第1周: 项目基础搭建
- 第2周: 认证和基础页面
- 第3-4周: 核心业务页面
- 第5周: 高级功能和优化

#### 集成测试 (1周)
- 前后端联调
- 端到端测试
- 性能优化
- 部署准备

### 里程碑节点

#### 里程碑1 (第2周末)
- 后端基础设施完成
- 前端项目框架搭建完成
- 基础组件库可用

#### 里程碑2 (第4周末)
- 用户认证功能完整
- 用户管理功能完整
- 基础权限控制可用

#### 里程碑3 (第6周末)
- RBAC权限系统完整
- 核心业务功能完整
- 前端主要页面完成

#### 里程碑4 (第8周末)
- 所有功能模块完成
- 系统集成测试通过
- 生产环境部署就绪

## 🎯 开发重点和注意事项

### 后端开发重点
1. **数据模型设计** - 确保扩展性和性能
2. **权限系统设计** - RBAC模型正确实现
3. **安全性考虑** - 认证、授权、数据保护
4. **性能优化** - 数据库查询、缓存策略
5. **错误处理** - 统一异常处理和日志记录

### 前端开发重点
1. **组件设计** - 可复用性和一致性
2. **状态管理** - 用户状态、权限状态管理
3. **用户体验** - 加载状态、错误提示、操作反馈
4. **权限控制** - 页面级和组件级权限控制
5. **性能优化** - 代码分割、懒加载、缓存

### 关键技术难点
1. **RBAC权限继承** - 角色层级和权限计算
2. **多租户隔离** - 数据隔离和权限隔离
3. **会话管理** - 分布式会话和并发控制
4. **安全防护** - XSS、CSRF、SQL注入防护
5. **性能优化** - 大数据量查询和前端渲染

## 🛠️ 具体实施指南

### 后端开发实施细节

#### 数据库设计最佳实践
```sql
-- 核心表设计示例
-- 1. 租户表 (tenants)
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_name VARCHAR(100) NOT NULL,
    tenant_code VARCHAR(50) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 2. 用户表 (users) - 租户隔离
CREATE TABLE users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, username),
    UNIQUE(tenant_id, email)
);

-- 3. 角色表 (roles)
CREATE TABLE roles (
    role_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    role_name VARCHAR(100) NOT NULL,
    role_code VARCHAR(50) NOT NULL,
    parent_role_id UUID REFERENCES roles(role_id),
    level INTEGER DEFAULT 1,
    UNIQUE(tenant_id, role_code)
);
```

#### API 设计规范
```python
# 统一请求响应格式
from pydantic import BaseModel
from typing import Generic, TypeVar, Optional

T = TypeVar('T')

class BaseRequest(BaseModel, Generic[T]):
    """统一请求格式"""
    data: T
    trace_id: Optional[str] = None
    timestamp: Optional[int] = None

class BaseResponse(BaseModel, Generic[T]):
    """统一响应格式"""
    status: str  # success, error
    code: int    # HTTP状态码
    message: str # 响应消息
    data: Optional[T] = None
    trace_id: Optional[str] = None
    timestamp: int

# 分页响应格式
class PaginatedResponse(BaseModel, Generic[T]):
    items: List[T]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool
```

#### 权限检查装饰器
```python
from functools import wraps
from fastapi import HTTPException, Depends
from typing import List

def require_permissions(permissions: List[str]):
    """权限检查装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从请求中获取用户信息
            current_user = kwargs.get('current_user')
            if not current_user:
                raise HTTPException(status_code=401, detail="未认证")

            # 检查权限
            user_permissions = await get_user_permissions(current_user.user_id)
            if not all(perm in user_permissions for perm in permissions):
                raise HTTPException(status_code=403, detail="权限不足")

            return await func(*args, **kwargs)
        return wrapper
    return decorator

# 使用示例
@router.post("/users/create")
@require_permissions(["user:create"])
async def create_user(request: CreateUserRequest):
    pass
```

### 前端开发实施细节

#### 权限控制组件
```typescript
// 权限守卫组件
interface PermissionGuardProps {
  permissions: string[]
  fallback?: React.ReactNode
  children: React.ReactNode
}

export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissions,
  fallback = null,
  children
}) => {
  const { hasPermissions } = useAuth()

  if (!hasPermissions(permissions)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

// 使用示例
<PermissionGuard
  permissions={['user:create']}
  fallback={<div>无权限访问</div>}
>
  <CreateUserButton />
</PermissionGuard>
```

#### API 客户端封装
```typescript
// API 客户端配置
import axios, { AxiosResponse } from 'axios'

interface ApiResponse<T> {
  status: string
  code: number
  message: string
  data: T
  trace_id?: string
  timestamp: number
}

class ApiClient {
  private client = axios.create({
    baseURL: '/api/v1',
    timeout: 10000,
  })

  constructor() {
    // 请求拦截器 - 添加认证头
    this.client.interceptors.request.use((config) => {
      const token = localStorage.getItem('access_token')
      if (token) {
        config.headers.Authorization = `Bearer ${token}`
      }
      return config
    })

    // 响应拦截器 - 统一错误处理
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          // 尝试刷新令牌
          await this.refreshToken()
          return this.client.request(error.config)
        }
        return Promise.reject(error)
      }
    )
  }

  async post<T>(url: string, data: any): Promise<T> {
    const response: AxiosResponse<ApiResponse<T>> = await this.client.post(url, {
      data,
      trace_id: generateTraceId(),
      timestamp: Date.now()
    })
    return response.data.data
  }

  private async refreshToken(): Promise<void> {
    // 刷新令牌逻辑
  }
}

export const apiClient = new ApiClient()
```

#### 状态管理最佳实践
```typescript
// 用户状态管理
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  user_id: string
  username: string
  email: string
  roles: string[]
  permissions: string[]
}

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  loading: boolean

  // Actions
  login: (credentials: LoginCredentials) => Promise<void>
  logout: () => void
  refreshUser: () => Promise<void>
  hasPermission: (permission: string) => boolean
  hasPermissions: (permissions: string[]) => boolean
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      loading: false,

      login: async (credentials) => {
        set({ loading: true })
        try {
          const response = await apiClient.post('/auth/login', credentials)
          const { user, access_token, refresh_token } = response

          localStorage.setItem('access_token', access_token)
          localStorage.setItem('refresh_token', refresh_token)

          set({ user, isAuthenticated: true, loading: false })
        } catch (error) {
          set({ loading: false })
          throw error
        }
      },

      logout: () => {
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        set({ user: null, isAuthenticated: false })
      },

      hasPermission: (permission) => {
        const { user } = get()
        return user?.permissions.includes(permission) ?? false
      },

      hasPermissions: (permissions) => {
        const { user } = get()
        return permissions.every(perm =>
          user?.permissions.includes(perm) ?? false
        )
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
```

## 📋 开发检查清单

### 后端开发检查清单

#### 基础设施
- [ ] 数据库连接配置正确
- [ ] Redis 缓存连接正常
- [ ] 依赖注入容器配置完整
- [ ] 日志系统配置正确
- [ ] 环境变量管理规范

#### API 开发
- [ ] 统一请求响应格式
- [ ] 参数验证完整
- [ ] 错误处理统一
- [ ] API 文档完整
- [ ] 接口测试覆盖

#### 安全性
- [ ] JWT 令牌管理正确
- [ ] 密码加密存储
- [ ] 权限检查完整
- [ ] SQL 注入防护
- [ ] XSS 攻击防护

#### 性能优化
- [ ] 数据库索引优化
- [ ] 查询语句优化
- [ ] 缓存策略合理
- [ ] 分页查询实现
- [ ] 异步处理优化

### 前端开发检查清单

#### 项目配置
- [ ] TypeScript 配置正确
- [ ] ESLint 规则配置
- [ ] Prettier 格式化配置
- [ ] 构建配置优化
- [ ] 环境变量管理

#### 组件开发
- [ ] 组件类型定义完整
- [ ] 组件复用性良好
- [ ] 组件测试覆盖
- [ ] 组件文档完整
- [ ] 组件性能优化

#### 用户体验
- [ ] 加载状态处理
- [ ] 错误状态处理
- [ ] 空状态处理
- [ ] 操作反馈及时
- [ ] 响应式设计

#### 权限控制
- [ ] 路由级权限控制
- [ ] 组件级权限控制
- [ ] 菜单权限过滤
- [ ] 按钮权限控制
- [ ] 数据权限控制

## 🚀 部署和运维

### 开发环境部署
```bash
# 后端启动
cd services/iam_service
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install -r requirements.txt
python main.py

# 前端启动
cd services/iam_service/frontend
npm install
npm run dev
```

### 生产环境部署
```dockerfile
# 后端 Dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8089

CMD ["python", "main.py"]
```

```dockerfile
# 前端 Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose 配置
```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: iam_db
      POSTGRES_USER: iam_user
      POSTGRES_PASSWORD: iam_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  iam-backend:
    build: .
    environment:
      DATABASE_URL: ************************************************/iam_db
      REDIS_URL: redis://redis:6379
    depends_on:
      - postgres
      - redis
    ports:
      - "8089:8089"

  iam-frontend:
    build: ./frontend
    ports:
      - "80:80"
    depends_on:
      - iam-backend

volumes:
  postgres_data:
```

## 📊 监控和维护

### 性能监控
```python
# 性能监控中间件
import time
from fastapi import Request, Response

async def performance_middleware(request: Request, call_next):
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time

    # 记录慢查询
    if process_time > 1.0:  # 超过1秒的请求
        logger.warning(f"Slow request: {request.url} took {process_time:.2f}s")

    response.headers["X-Process-Time"] = str(process_time)
    return response
```

### 健康检查
```python
@router.get("/health")
async def health_check():
    """系统健康检查"""
    checks = {
        "database": await check_database_connection(),
        "redis": await check_redis_connection(),
        "external_services": await check_external_services()
    }

    all_healthy = all(checks.values())
    status_code = 200 if all_healthy else 503

    return JSONResponse(
        status_code=status_code,
        content={
            "status": "healthy" if all_healthy else "unhealthy",
            "checks": checks,
            "timestamp": datetime.utcnow().isoformat()
        }
    )
```

### 日志管理
```python
import logging
from pythonjsonlogger import jsonlogger

# 配置结构化日志
def setup_logging():
    logger = logging.getLogger()
    handler = logging.StreamHandler()
    formatter = jsonlogger.JsonFormatter(
        '%(asctime)s %(name)s %(levelname)s %(message)s'
    )
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
```

## 🔧 故障排除指南

### 常见问题和解决方案

#### 1. 数据库连接问题
```bash
# 检查数据库连接
psql -h localhost -U iam_user -d iam_db

# 检查连接池状态
SELECT * FROM pg_stat_activity WHERE datname = 'iam_db';
```

#### 2. Redis 连接问题
```bash
# 检查 Redis 连接
redis-cli ping

# 检查 Redis 内存使用
redis-cli info memory
```

#### 3. 权限问题排查
```python
# 权限调试工具
async def debug_user_permissions(user_id: str, tenant_id: str):
    """调试用户权限"""
    user_roles = await get_user_roles(user_id, tenant_id)
    role_permissions = []

    for role in user_roles:
        permissions = await get_role_permissions(role.role_id)
        role_permissions.extend(permissions)

    return {
        "user_id": user_id,
        "roles": [r.role_name for r in user_roles],
        "permissions": [p.permission_code for p in role_permissions]
    }
```

#### 4. 性能问题排查
```sql
-- 查找慢查询
SELECT query, mean_time, calls, total_time
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 检查索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

---

**注意**: 本开发顺序基于功能依赖关系和业务优先级制定，实际开发中可根据团队情况和项目需求进行调整。建议严格按照里程碑节点进行进度控制，确保项目按时交付。

**重要提醒**:
1. 开发过程中要注重代码质量和安全性
2. 及时编写测试用例和文档
3. 定期进行代码审查和重构
4. 保持前后端接口文档同步
5. 重视用户体验和性能优化
