/**
 * @file 表单验证工具
 * @description 提供常用的表单验证规则和验证函数
 * @status 开发中
 */

export interface ValidationRule {
  required?: boolean
  minLength?: number
  maxLength?: number
  pattern?: RegExp
  custom?: (value: any) => string | null
  message?: string
}

export interface ValidationResult {
  isValid: boolean
  errors: Record<string, string>
}

/**
 * 验证单个字段
 */
export function validateField(value: any, rules: ValidationRule): string | null {
  // 必填验证
  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
    return rules.message || '此字段为必填项'
  }

  // 如果值为空且不是必填，则跳过其他验证
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return null
  }

  const stringValue = String(value)

  // 最小长度验证
  if (rules.minLength && stringValue.length < rules.minLength) {
    return rules.message || `最少需要${rules.minLength}个字符`
  }

  // 最大长度验证
  if (rules.maxLength && stringValue.length > rules.maxLength) {
    return rules.message || `最多允许${rules.maxLength}个字符`
  }

  // 正则表达式验证
  if (rules.pattern && !rules.pattern.test(stringValue)) {
    return rules.message || '格式不正确'
  }

  // 自定义验证
  if (rules.custom) {
    return rules.custom(value)
  }

  return null
}

/**
 * 验证整个表单
 */
export function validateForm(data: Record<string, any>, rules: Record<string, ValidationRule>): ValidationResult {
  const errors: Record<string, string> = {}

  for (const [field, rule] of Object.entries(rules)) {
    const error = validateField(data[field], rule)
    if (error) {
      errors[field] = error
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

/**
 * 邮箱验证规则
 */
export const emailRule: ValidationRule = {
  required: true,
  pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  message: '请输入有效的邮箱地址'
}

/**
 * 密码验证规则
 */
export const passwordRule: ValidationRule = {
  required: true,
  minLength: 8,
  pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
  message: '密码必须包含至少8个字符，包括大小写字母、数字和特殊字符'
}

/**
 * 简单密码验证规则（用于登录）
 */
export const simplePasswordRule: ValidationRule = {
  required: true,
  minLength: 6,
  message: '密码至少需要6个字符'
}

/**
 * 用户名验证规则
 */
export const usernameRule: ValidationRule = {
  required: true,
  minLength: 3,
  maxLength: 20,
  pattern: /^[a-zA-Z0-9_-]+$/,
  message: '用户名只能包含字母、数字、下划线和连字符，长度3-20个字符'
}

/**
 * 手机号验证规则
 */
export const phoneRule: ValidationRule = {
  required: true,
  pattern: /^1[3-9]\d{9}$/,
  message: '请输入有效的手机号码'
}

/**
 * 租户ID验证规则
 */
export const tenantIdRule: ValidationRule = {
  required: true,
  message: '请选择租户'
}

/**
 * 租户编码验证规则
 */
export const tenantCodeRule: ValidationRule = {
  required: true,
  minLength: 2,
  maxLength: 50,
  pattern: /^[a-zA-Z0-9_-]+$/,
  message: '租户编码只能包含字母、数字、下划线和连字符，长度2-50个字符'
}

/**
 * 验证码验证规则
 */
export const verificationCodeRule: ValidationRule = {
  required: true,
  minLength: 4,
  maxLength: 8,
  pattern: /^[0-9]+$/,
  message: '请输入4-8位数字验证码'
}

/**
 * 昵称验证规则
 */
export const nicknameRule: ValidationRule = {
  required: false,
  minLength: 1,
  maxLength: 50,
  message: '昵称长度1-50个字符'
}

/**
 * 确认密码验证
 */
export function createConfirmPasswordRule(originalPassword: string): ValidationRule {
  return {
    required: true,
    custom: (value: string) => {
      if (value !== originalPassword) {
        return '两次输入的密码不一致'
      }
      return null
    }
  }
}

/**
 * 登录表单验证规则
 */
export const loginFormRules = {
  tenant_id: tenantIdRule,
  identifier: emailRule,
  credential: simplePasswordRule
}

/**
 * 注册表单验证规则
 */
export const registerFormRules = {
  tenant_code: tenantCodeRule,
  username: usernameRule,
  email: emailRule,
  phone: { ...phoneRule, required: false },
  nickname: nicknameRule,
  password: passwordRule,
  verification_code: verificationCodeRule,
  agree_terms: {
    required: true,
    custom: (value: boolean) => {
      if (!value) {
        return '必须同意服务条款'
      }
      return null
    }
  }
}

/**
 * 发送验证码表单验证规则
 */
export const sendCodeFormRules = {
  email: emailRule
}

/**
 * 修改密码表单验证规则
 */
export function createChangePasswordRules(newPassword: string) {
  return {
    old_password: simplePasswordRule,
    new_password: passwordRule,
    confirm_password: createConfirmPasswordRule(newPassword)
  }
}
