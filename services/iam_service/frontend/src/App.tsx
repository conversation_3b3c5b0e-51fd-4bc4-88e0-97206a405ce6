/**
 * @file 主应用组件
 * @description 应用程序的根组件，负责路由管理和全局布局
 * @status 框架文件 - 完成
 */

import React, { useEffect, useRef, useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { Layout, AuthLayout } from '@/components/layout/Layout'
import { EventBusProvider } from '@/utils/EventBus'
import { ErrorBoundary } from '@/components/common/ErrorBoundary'
import { DevTools } from '@/utils/DevTools'
import { moduleRegistry } from '@/utils/ModuleLoader'
import { dashboardModule } from '@/modules/dashboard'
import { LoginPage, RegisterPage, ActivatePage } from '@/modules/auth'
import { useAuth } from '@/stores/authStore'
import { PageLoading } from '@/components/common/LoadingSpinner'
import toast, { Toaster } from 'react-hot-toast'

function App() {
    const initialized = useRef(false)
    const [modulesLoaded, setModulesLoaded] = useState(false)
    const { isAuthenticated, isLoading, checkAuthStatus } = useAuth()

    useEffect(() => {
        // 防止在 React StrictMode 下重复初始化
        if (initialized.current) {
            return
        }
        initialized.current = true

        const initializeApp = async () => {
            try {
                // 检查认证状态
                await checkAuthStatus()

                // 注册模块
                moduleRegistry.register(dashboardModule)

                // 加载默认模块
                await moduleRegistry.load('dashboard')

                // 标记模块已加载，触发重新渲染
                setModulesLoaded(true)
            } catch (error) {
                console.error('Failed to initialize app:', error)
                toast.error('应用初始化失败')
                setModulesLoaded(true) // 即使失败也要继续
            }
        }

        initializeApp()
    }, [checkAuthStatus])

    // 应用初始化加载中
    if (isLoading || !modulesLoaded) {
        return <PageLoading text="正在初始化应用..." />
    }

    return (
        <ErrorBoundary>
            <EventBusProvider>
                <Router
                    future={{
                        v7_startTransition: true,
                        v7_relativeSplatPath: true,
                    }}
                >
                    <Routes>
                        {/* 认证相关路由 */}
                        <Route
                            path="/login"
                            element={
                                <AuthLayout>
                                    <LoginPage />
                                </AuthLayout>
                            }
                        />
                        <Route
                            path="/register"
                            element={
                                <AuthLayout>
                                    <RegisterPage />
                                </AuthLayout>
                            }
                        />
                        <Route
                            path="/activate"
                            element={
                                <AuthLayout>
                                    <ActivatePage />
                                </AuthLayout>
                            }
                        />

                        {/* 受保护的路由 */}
                        <Route
                            path="/*"
                            element={
                                isAuthenticated ? (
                                    <Layout>
                                        <Routes>
                                            {/* 默认重定向到首页 */}
                                            <Route path="/" element={<Navigate to="/dashboard" replace />} />

                                            {/* 动态加载模块路由 */}
                                            {moduleRegistry.getAllRoutes().map((route, index) => (
                                                <Route key={index} {...route} />
                                            ))}

                                            {/* 404 页面 */}
                                            <Route path="*" element={
                                                <div className="p-6 text-center">
                                                    <h1 className="text-2xl font-bold text-gray-900 mb-4">404 - 页面未找到</h1>
                                                    <p className="text-gray-600">您访问的页面不存在</p>
                                                </div>
                                            } />
                                        </Routes>
                                    </Layout>
                                ) : (
                                    <Navigate to="/login" replace />
                                )
                            }
                        />
                    </Routes>
                </Router>

                {/* 全局通知 */}
                <Toaster
                    position="top-right"
                    toastOptions={{
                        duration: 4000,
                        style: {
                            background: '#363636',
                            color: '#fff',
                        },
                    }}
                />

                {/* 开发工具 */}
                <DevTools />
            </EventBusProvider>
        </ErrorBoundary>
    )
}

export default App