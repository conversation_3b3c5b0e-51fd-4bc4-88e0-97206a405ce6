/**
 * @file 权限守卫组件
 * @description 基于用户权限控制组件显示的守卫组件
 * @status 开发中
 */

import React from 'react'
import { usePermissions } from '@/stores/authStore'

export interface PermissionGuardProps {
  /** 需要的权限列表 */
  permissions?: string[]
  /** 需要的单个权限 */
  permission?: string
  /** 权限检查模式：all(需要所有权限) | any(需要任意权限) */
  mode?: 'all' | 'any'
  /** 无权限时显示的内容 */
  fallback?: React.ReactNode
  /** 子组件 */
  children: React.ReactNode
  /** 是否在无权限时隐藏（不显示fallback） */
  hideOnNoPermission?: boolean
}

/**
 * 权限守卫组件
 * 根据用户权限决定是否显示子组件
 */
export const PermissionGuard: React.FC<PermissionGuardProps> = ({
  permissions = [],
  permission,
  mode = 'all',
  fallback = null,
  children,
  hideOnNoPermission = false
}) => {
  const { hasPermission, hasPermissions, hasAnyPermission } = usePermissions()

  // 构建权限列表
  const permissionList = permission ? [permission] : permissions

  // 如果没有指定权限，直接显示子组件
  if (permissionList.length === 0) {
    return <>{children}</>
  }

  // 检查权限
  let hasRequiredPermission = false

  if (mode === 'all') {
    hasRequiredPermission = hasPermissions(permissionList)
  } else {
    hasRequiredPermission = hasAnyPermission(permissionList)
  }

  // 有权限时显示子组件
  if (hasRequiredPermission) {
    return <>{children}</>
  }

  // 无权限时的处理
  if (hideOnNoPermission) {
    return null
  }

  return <>{fallback}</>
}

/**
 * 默认无权限提示组件
 */
export const NoPermissionFallback: React.FC<{ message?: string }> = ({ 
  message = '您没有权限访问此内容' 
}) => {
  return (
    <div className="flex flex-col items-center justify-center py-12 text-gray-500">
      <svg
        className="h-12 w-12 mb-4"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
        />
      </svg>
      <p className="text-sm">{message}</p>
    </div>
  )
}

/**
 * 带权限的按钮组件
 */
interface PermissionButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  permission?: string
  permissions?: string[]
  mode?: 'all' | 'any'
  hideOnNoPermission?: boolean
  children: React.ReactNode
}

export const PermissionButton: React.FC<PermissionButtonProps> = ({
  permission,
  permissions,
  mode = 'all',
  hideOnNoPermission = true,
  children,
  ...buttonProps
}) => {
  return (
    <PermissionGuard
      permission={permission}
      permissions={permissions}
      mode={mode}
      hideOnNoPermission={hideOnNoPermission}
    >
      <button {...buttonProps}>
        {children}
      </button>
    </PermissionGuard>
  )
}

/**
 * 权限检查Hook
 */
export const usePermissionCheck = () => {
  const { hasPermission, hasPermissions, hasAnyPermission } = usePermissions()

  const checkPermission = (permission: string): boolean => {
    return hasPermission(permission)
  }

  const checkPermissions = (permissions: string[], mode: 'all' | 'any' = 'all'): boolean => {
    if (mode === 'all') {
      return hasPermissions(permissions)
    } else {
      return hasAnyPermission(permissions)
    }
  }

  return {
    checkPermission,
    checkPermissions,
    hasPermission,
    hasPermissions,
    hasAnyPermission
  }
}

/**
 * 权限装饰器HOC
 */
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermissions: string[],
  mode: 'all' | 'any' = 'all',
  fallback?: React.ReactNode
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGuard
        permissions={requiredPermissions}
        mode={mode}
        fallback={fallback || <NoPermissionFallback />}
      >
        <Component {...props} />
      </PermissionGuard>
    )
  }
}

/**
 * 路由权限守卫组件
 */
interface RoutePermissionGuardProps {
  permissions: string[]
  mode?: 'all' | 'any'
  redirectTo?: string
  children: React.ReactNode
}

export const RoutePermissionGuard: React.FC<RoutePermissionGuardProps> = ({
  permissions,
  mode = 'all',
  redirectTo = '/403',
  children
}) => {
  const { hasPermissions, hasAnyPermission } = usePermissions()

  const hasRequiredPermission = mode === 'all' 
    ? hasPermissions(permissions)
    : hasAnyPermission(permissions)

  if (!hasRequiredPermission) {
    // 在实际应用中，这里应该使用路由导航
    // 例如：navigate(redirectTo)
    window.location.href = redirectTo
    return null
  }

  return <>{children}</>
}

// 常用权限常量
export const PERMISSIONS = {
  // 用户管理
  USER_VIEW: 'user:view',
  USER_CREATE: 'user:create',
  USER_UPDATE: 'user:update',
  USER_DELETE: 'user:delete',
  
  // 角色管理
  ROLE_VIEW: 'role:view',
  ROLE_CREATE: 'role:create',
  ROLE_UPDATE: 'role:update',
  ROLE_DELETE: 'role:delete',
  
  // 权限管理
  PERMISSION_VIEW: 'permission:view',
  PERMISSION_CREATE: 'permission:create',
  PERMISSION_UPDATE: 'permission:update',
  PERMISSION_DELETE: 'permission:delete',
  
  // 租户管理
  TENANT_VIEW: 'tenant:view',
  TENANT_CREATE: 'tenant:create',
  TENANT_UPDATE: 'tenant:update',
  TENANT_DELETE: 'tenant:delete',
  
  // 系统管理
  SYSTEM_CONFIG: 'system:config',
  SYSTEM_MONITOR: 'system:monitor',
  
  // 审计日志
  AUDIT_VIEW: 'audit:view',
  AUDIT_EXPORT: 'audit:export'
} as const
