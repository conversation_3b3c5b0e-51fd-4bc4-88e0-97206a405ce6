/**
 * @file API类型定义
 * @description 定义与后端API交互的所有类型接口
 * @status 框架文件 - 完成
 */

// 基础响应类型 - 根据IAM服务API规范
export interface BaseResponse<T = any> {
  status: string  // success, error
  code: number    // HTTP状态码
  message: string // 响应消息
  data?: T
  trace_id?: string
  timestamp: number
}

// 成功响应类型
export interface SuccessResponse<T = any> extends BaseResponse<T> {
  status: 'success'
  data: T
}

// 错误响应类型
export interface ErrorResponse extends BaseResponse<null> {
  status: 'error'
  data: null
}

// 分页响应类型
export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  has_next: boolean
  has_prev: boolean
}

// API错误类型
export interface ApiError {
  status: 'error'
  code: number
  message: string
  trace_id?: string
  timestamp: number
}

// 设备信息类型
export interface DeviceInfo {
  device_name?: string
  device_type?: string
  os?: string
  browser?: string
  ip_address?: string
  user_agent?: string
  location?: string
}

// 用户信息类型
export interface UserInfo {
  user_id: string
  username: string
  email: string
  display_name?: string
  avatar_url?: string
  phone?: string
  status: string
  created_at: string
  updated_at: string
  last_login_at?: string
}

// 租户信息类型
export interface TenantInfo {
  tenant_id: string
  tenant_name: string
  tenant_code: string
  status: string
  created_at: string
}

// 认证相关类型 - 根据IAM服务API规范
export interface LoginRequest {
  tenant_id: string
  login_type: 'username' | 'email' | 'phone'
  identifier: string  // 用户标识符（用户名、邮箱或手机号）
  credential: string  // 密码或验证码
  remember_me?: boolean
  mfa_code?: string
  device_info?: DeviceInfo
}

export interface LoginResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  refresh_expires_in: number
  session_id: string
  user_info: UserInfo
  tenant_info: TenantInfo
  permissions: string[]
  security_warnings?: string[]
}

export interface RefreshTokenRequest {
  tenant_id: string
  refresh_token: string
}

export interface RefreshTokenResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  refresh_expires_in: number
}

// 登出请求类型
export interface LogoutRequest {
  tenant_id: string
  session_id?: string
  logout_all_devices?: boolean
}

// 修改密码请求类型
export interface ChangePasswordRequest {
  tenant_id: string
  user_id: string
  old_password: string
  new_password: string
  logout_other_sessions?: boolean
}

// 用户注册请求类型
export interface RegisterRequest {
  tenant_code: string
  username: string
  email: string
  phone?: string
  nickname?: string
  password: string
  verification_code: string
  code_id: string
  agree_terms: boolean
}

// 用户注册响应类型
export interface RegisterResponse {
  user_id: string
  username: string
  email: string
  status: string
  activation_required: boolean
  created_at: string
}

// 发送邮箱验证码请求类型
export interface SendEmailCodeRequest {
  tenant_id: string
  email: string
  scene: string
  user_id?: string
}

// 发送邮箱验证码响应类型
export interface SendEmailCodeResponse {
  target: string
  code_id: string
  expire_seconds: number
  sent_at: string
}

// 激活用户请求类型
export interface ActivateUserRequest {
  activation_token: string
}

// 激活用户响应类型
export interface ActivateUserResponse {
  user_id: string
  status: string
  activated_at: string
}

// 获取租户信息请求类型
export interface GetTenantByCodeRequest {
  tenant_code: string
}

// 获取租户信息响应类型
export interface GetTenantByCodeResponse {
  tenant_id: string
  tenant_name: string
  tenant_code: string
  status: string
}

// 通用查询参数
export interface QueryParams {
  page?: number
  size?: number
  sort_by?: string
  sort_order?: 'asc' | 'desc'
  search?: string
  filters?: Record<string, any>
}

// HTTP方法类型
export type HttpMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'

// 请求配置类型
export interface RequestConfig {
  method?: HttpMethod
  headers?: Record<string, string>
  timeout?: number
  retries?: number
  params?: Record<string, any>
  data?: any
}

// 请求元信息
export interface RequestMeta {
  request_id?: string
  timestamp: string
  version: string
}

// 基础请求格式 - 根据IAM服务API规范
export interface BaseRequest<T = any> {
  meta: RequestMeta
  data: T
}