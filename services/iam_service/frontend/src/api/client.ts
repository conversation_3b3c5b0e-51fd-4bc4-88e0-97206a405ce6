/**
 * @file API客户端
 * @description 基于axios的HTTP客户端，提供统一的API请求接口和错误处理
 * @status 框架文件 - 完成
 */

import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { BaseResponse, SuccessResponse, ErrorResponse, ApiError, RequestConfig } from './types'

class ApiClient {
  private instance: AxiosInstance
  private baseURL: string

  constructor(baseURL = '/api') {
    this.baseURL = baseURL
    this.instance = axios.create({
      baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = this.getAuthToken()
        if (token && config.headers) {
          config.headers.Authorization = `Bearer ${token}`
        }

        // 添加请求ID用于追踪
        if (config.headers) {
          config.headers['X-Request-ID'] = this.generateRequestId()
        }

        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data,
        })

        return config
      },
      (error) => {
        console.error('[API] Request error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<BaseResponse>) => {
        console.log(`[API] Response:`, response.data)
        return response
      },
      async (error) => {
        console.error('[API] Response error:', error)

        // 处理认证错误
        if (error.response?.status === 401) {
          await this.handleAuthError()
        }

        // 处理网络错误
        if (!error.response) {
          return Promise.reject({
            status: 'error',
            code: 0,
            message: '网络连接错误，请检查网络设置',
            timestamp: Date.now(),
          } as ApiError)
        }

        // 处理服务器错误
        const apiError: ApiError = {
          status: 'error',
          code: error.response.status,
          message: error.response.data?.message || '服务器错误',
          trace_id: error.response.data?.trace_id,
          timestamp: error.response.data?.timestamp || Date.now(),
        }

        return Promise.reject(apiError)
      }
    )
  }

  private getAuthToken(): string | null {
    return localStorage.getItem('access_token')
  }

  private setAuthToken(token: string) {
    localStorage.setItem('access_token', token)
  }

  private removeAuthToken() {
    localStorage.removeItem('access_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('session_id')
    localStorage.removeItem('tenant_id')
    localStorage.removeItem('user_info')
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private async handleAuthError() {
    // 尝试刷新token
    const refreshToken = localStorage.getItem('refresh_token')
    const tenantId = localStorage.getItem('tenant_id')

    if (refreshToken && tenantId) {
      try {
        const response = await this.post<{ access_token: string; refresh_token: string }>('/v1/auth/refresh', {
          data: {
            tenant_id: tenantId,
            refresh_token: refreshToken,
          },
          trace_id: this.generateRequestId(),
          timestamp: Date.now()
        })

        if (response.status === 'success' && response.data) {
          this.setAuthToken(response.data.access_token)
          localStorage.setItem('refresh_token', response.data.refresh_token)
          return
        }
      } catch (error) {
        console.error('Token refresh failed:', error)
      }
    }

    // 清除认证信息并重定向到登录页
    this.removeAuthToken()
    window.location.href = '/login'
  }

  // GET请求
  async get<T = any>(url: string, config?: RequestConfig): Promise<BaseResponse<T>> {
    const response = await this.instance.get<BaseResponse<T>>(url, {
      params: config?.params,
      timeout: config?.timeout,
      headers: config?.headers,
    })
    return response.data
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: RequestConfig): Promise<BaseResponse<T>> {
    const response = await this.instance.post<BaseResponse<T>>(url, data, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: RequestConfig): Promise<BaseResponse<T>> {
    const response = await this.instance.put<BaseResponse<T>>(url, data, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: RequestConfig): Promise<BaseResponse<T>> {
    const response = await this.instance.delete<BaseResponse<T>>(url, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // PATCH请求
  async patch<T = any>(url: string, data?: any, config?: RequestConfig): Promise<BaseResponse<T>> {
    const response = await this.instance.patch<BaseResponse<T>>(url, data, {
      timeout: config?.timeout,
      headers: config?.headers,
      params: config?.params,
    })
    return response.data
  }

  // 上传文件
  async upload<T = any>(url: string, file: File, config?: RequestConfig): Promise<BaseResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    const response = await this.instance.post<BaseResponse<T>>(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        ...config?.headers,
      },
      timeout: config?.timeout || 30000, // 上传超时时间更长
      params: config?.params,
    })
    return response.data
  }

  // 设置基础URL
  setBaseURL(baseURL: string) {
    this.baseURL = baseURL
    this.instance.defaults.baseURL = baseURL
  }

  // 设置默认headers
  setDefaultHeaders(headers: Record<string, string>) {
    Object.assign(this.instance.defaults.headers.common, headers)
  }
}

// 创建默认实例
const apiClient = new ApiClient()

export { apiClient, ApiClient }
export default apiClient 