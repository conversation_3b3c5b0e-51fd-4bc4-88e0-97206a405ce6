/**
 * @file 登录表单组件
 * @description 用户邮箱登录表单，支持表单验证和错误处理
 * @status 开发中
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { useAuth } from '@/stores/authStore'
import { validateForm, loginFormRules } from '@/utils/validation'
import { LoginRequest } from '@/api/types'

export interface LoginFormProps {
  onSuccess?: () => void
  onError?: (error: string) => void
  className?: string
}

export const LoginForm: React.FC<LoginFormProps> = ({
  onSuccess,
  onError,
  className
}) => {
  const { login, isLoading, error, clearError } = useAuth()
  
  // 表单数据
  const [formData, setFormData] = useState<LoginRequest>({
    tenant_id: '',
    login_type: 'email',
    identifier: '',
    credential: '',
    remember_me: false
  })
  
  // 表单验证错误
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  
  // 字段是否被触摸过
  const [touched, setTouched] = useState<Record<string, boolean>>({})

  // 处理输入变化
  const handleInputChange = (field: keyof LoginRequest) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value
    
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // 清除该字段的验证错误
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }

    // 清除全局错误
    if (error) {
      clearError()
    }
  }

  // 处理字段失焦
  const handleBlur = (field: keyof LoginRequest) => () => {
    setTouched(prev => ({
      ...prev,
      [field]: true
    }))

    // 验证单个字段
    const result = validateForm({ [field]: formData[field] }, { [field]: loginFormRules[field] })
    if (result.errors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: result.errors[field]
      }))
    }
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 验证表单
    const validation = validateForm(formData, loginFormRules)
    
    if (!validation.isValid) {
      setValidationErrors(validation.errors)
      setTouched({
        tenant_id: true,
        identifier: true,
        credential: true
      })
      return
    }

    try {
      await login(formData)
      onSuccess?.()
    } catch (err: any) {
      const errorMessage = err.message || '登录失败，请重试'
      onError?.(errorMessage)
    }
  }

  // 获取字段错误信息
  const getFieldError = (field: keyof LoginRequest) => {
    return touched[field] ? validationErrors[field] : ''
  }

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-gray-900">
          登录
        </CardTitle>
        <p className="text-gray-600 mt-2">
          请输入您的邮箱和密码
        </p>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 租户ID选择 - 暂时使用输入框，后续可改为下拉选择 */}
          <Input
            label="租户ID"
            type="text"
            placeholder="请输入租户ID"
            value={formData.tenant_id}
            onChange={handleInputChange('tenant_id')}
            onBlur={handleBlur('tenant_id')}
            error={getFieldError('tenant_id')}
            disabled={isLoading}
            required
          />

          {/* 邮箱输入 */}
          <Input
            label="邮箱地址"
            type="email"
            placeholder="请输入邮箱地址"
            value={formData.identifier}
            onChange={handleInputChange('identifier')}
            onBlur={handleBlur('identifier')}
            error={getFieldError('identifier')}
            disabled={isLoading}
            required
            leftIcon={
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            }
          />

          {/* 密码输入 */}
          <Input
            label="密码"
            type="password"
            placeholder="请输入密码"
            value={formData.credential}
            onChange={handleInputChange('credential')}
            onBlur={handleBlur('credential')}
            error={getFieldError('credential')}
            disabled={isLoading}
            required
            leftIcon={
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            }
          />

          {/* 记住我 */}
          <div className="flex items-center">
            <input
              id="remember-me"
              type="checkbox"
              checked={formData.remember_me}
              onChange={handleInputChange('remember_me')}
              disabled={isLoading}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-700">
              记住我
            </label>
          </div>

          {/* 全局错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 提交按钮 */}
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isLoading}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? '登录中...' : '登录'}
          </Button>
        </form>

        {/* 其他操作链接 */}
        <div className="mt-6 text-center space-y-2">
          <div className="text-sm">
            <a
              href="#"
              className="text-blue-600 hover:text-blue-500 hover:underline"
              onClick={(e) => {
                e.preventDefault()
                // TODO: 实现忘记密码功能
                console.log('忘记密码功能待实现')
              }}
            >
              忘记密码？
            </a>
          </div>
          
          <div className="text-sm text-gray-600">
            还没有账户？{' '}
            <a
              href="/register"
              className="text-blue-600 hover:text-blue-500 hover:underline"
              onClick={(e) => {
                e.preventDefault()
                window.location.href = '/register'
              }}
            >
              立即注册
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
