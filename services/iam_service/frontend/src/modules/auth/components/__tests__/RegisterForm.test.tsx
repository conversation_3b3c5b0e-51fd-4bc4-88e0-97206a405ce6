/**
 * @file 注册表单组件测试
 * @description 测试注册表单的功能和交互
 * @status 开发中
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { RegisterForm } from '../RegisterForm'

// Mock API
vi.mock('@/api/auth', () => ({
  register: vi.fn(),
  sendEmailCode: vi.fn()
}))

// Mock utils
vi.mock('@/utils/common', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' ')
}))

describe('RegisterForm', () => {
  const mockOnSuccess = vi.fn()
  const mockOnError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders register form correctly', () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    expect(screen.getByText('注册账户')).toBeInTheDocument()
    expect(screen.getByText('创建您的新账户')).toBeInTheDocument()
    expect(screen.getByLabelText('租户编码')).toBeInTheDocument()
    expect(screen.getByLabelText('用户名')).toBeInTheDocument()
    expect(screen.getByLabelText('邮箱地址')).toBeInTheDocument()
    expect(screen.getByLabelText('手机号')).toBeInTheDocument()
    expect(screen.getByLabelText('昵称')).toBeInTheDocument()
    expect(screen.getByLabelText('密码')).toBeInTheDocument()
    expect(screen.getByLabelText('邮箱验证码')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '获取验证码' })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '注册账户' })).toBeInTheDocument()
  })

  it('shows validation errors for empty required fields', async () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const submitButton = screen.getByRole('button', { name: '注册账户' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getAllByText('此字段为必填项')).toHaveLength(5) // 租户编码、用户名、邮箱、密码、验证码
    })
  })

  it('validates email format', async () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const emailInput = screen.getByLabelText('邮箱地址')
    
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    fireEvent.blur(emailInput)

    await waitFor(() => {
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
    })
  })

  it('validates username format', async () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const usernameInput = screen.getByLabelText('用户名')
    
    fireEvent.change(usernameInput, { target: { value: 'ab' } })
    fireEvent.blur(usernameInput)

    await waitFor(() => {
      expect(screen.getByText('用户名只能包含字母、数字、下划线和连字符，长度3-20个字符')).toBeInTheDocument()
    })
  })

  it('validates password strength', async () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const passwordInput = screen.getByLabelText('密码')
    
    fireEvent.change(passwordInput, { target: { value: 'weak' } })
    fireEvent.blur(passwordInput)

    await waitFor(() => {
      expect(screen.getByText('密码必须包含至少8个字符，包括大小写字母、数字和特殊字符')).toBeInTheDocument()
    })
  })

  it('sends verification code when email is valid', async () => {
    const mockSendEmailCode = vi.fn().mockResolvedValue({
      code_id: 'test-code-id',
      expire_seconds: 300,
      sent_at: new Date().toISOString()
    })
    
    vi.mocked(require('@/api/auth').sendEmailCode).mockImplementation(mockSendEmailCode)

    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const emailInput = screen.getByLabelText('邮箱地址')
    const sendCodeButton = screen.getByRole('button', { name: '获取验证码' })
    
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.click(sendCodeButton)

    await waitFor(() => {
      expect(mockSendEmailCode).toHaveBeenCalledWith({
        email: '<EMAIL>',
        scene: 'register'
      })
    })

    // 检查倒计时是否开始
    await waitFor(() => {
      expect(screen.getByText(/\d+s/)).toBeInTheDocument()
    })
  })

  it('disables send code button when email is invalid', () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const sendCodeButton = screen.getByRole('button', { name: '获取验证码' })
    
    expect(sendCodeButton).toBeDisabled()
  })

  it('validates terms agreement', async () => {
    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const submitButton = screen.getByRole('button', { name: '注册账户' })
    
    // 填写所有必填字段但不同意条款
    fireEvent.change(screen.getByLabelText('租户编码'), { target: { value: 'test-tenant' } })
    fireEvent.change(screen.getByLabelText('用户名'), { target: { value: 'testuser' } })
    fireEvent.change(screen.getByLabelText('邮箱地址'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('密码'), { target: { value: 'Password123!' } })
    fireEvent.change(screen.getByLabelText('邮箱验证码'), { target: { value: '123456' } })
    
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('必须同意服务条款')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const mockRegister = vi.fn().mockResolvedValue({
      user_id: 'test-user-id',
      username: 'testuser',
      email: '<EMAIL>',
      status: 'pending_activation',
      activation_required: true,
      created_at: new Date().toISOString()
    })
    
    const mockSendEmailCode = vi.fn().mockResolvedValue({
      code_id: 'test-code-id',
      expire_seconds: 300,
      sent_at: new Date().toISOString()
    })
    
    vi.mocked(require('@/api/auth').register).mockImplementation(mockRegister)
    vi.mocked(require('@/api/auth').sendEmailCode).mockImplementation(mockSendEmailCode)

    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    // 填写表单
    fireEvent.change(screen.getByLabelText('租户编码'), { target: { value: 'test-tenant' } })
    fireEvent.change(screen.getByLabelText('用户名'), { target: { value: 'testuser' } })
    fireEvent.change(screen.getByLabelText('邮箱地址'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('密码'), { target: { value: 'Password123!' } })
    
    // 发送验证码
    fireEvent.click(screen.getByRole('button', { name: '获取验证码' }))
    
    await waitFor(() => {
      expect(mockSendEmailCode).toHaveBeenCalled()
    })
    
    // 输入验证码并同意条款
    fireEvent.change(screen.getByLabelText('邮箱验证码'), { target: { value: '123456' } })
    fireEvent.click(screen.getByLabelText(/我已阅读并同意/))
    
    // 提交表单
    fireEvent.click(screen.getByRole('button', { name: '注册账户' }))

    await waitFor(() => {
      expect(mockRegister).toHaveBeenCalledWith({
        tenant_code: 'test-tenant',
        username: 'testuser',
        email: '<EMAIL>',
        phone: '',
        nickname: '',
        password: 'Password123!',
        verification_code: '123456',
        code_id: 'test-code-id',
        agree_terms: true
      })
    })

    expect(mockOnSuccess).toHaveBeenCalled()
  })

  it('handles registration error', async () => {
    const mockRegister = vi.fn().mockRejectedValue(new Error('注册失败'))
    const mockSendEmailCode = vi.fn().mockResolvedValue({
      code_id: 'test-code-id',
      expire_seconds: 300,
      sent_at: new Date().toISOString()
    })
    
    vi.mocked(require('@/api/auth').register).mockImplementation(mockRegister)
    vi.mocked(require('@/api/auth').sendEmailCode).mockImplementation(mockSendEmailCode)

    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    // 填写表单并提交
    fireEvent.change(screen.getByLabelText('租户编码'), { target: { value: 'test-tenant' } })
    fireEvent.change(screen.getByLabelText('用户名'), { target: { value: 'testuser' } })
    fireEvent.change(screen.getByLabelText('邮箱地址'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('密码'), { target: { value: 'Password123!' } })
    
    fireEvent.click(screen.getByRole('button', { name: '获取验证码' }))
    
    await waitFor(() => {
      expect(mockSendEmailCode).toHaveBeenCalled()
    })
    
    fireEvent.change(screen.getByLabelText('邮箱验证码'), { target: { value: '123456' } })
    fireEvent.click(screen.getByLabelText(/我已阅读并同意/))
    fireEvent.click(screen.getByRole('button', { name: '注册账户' }))

    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith('注册失败')
    })
  })

  it('shows loading state during registration', async () => {
    const mockRegister = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)))
    const mockSendEmailCode = vi.fn().mockResolvedValue({
      code_id: 'test-code-id',
      expire_seconds: 300,
      sent_at: new Date().toISOString()
    })
    
    vi.mocked(require('@/api/auth').register).mockImplementation(mockRegister)
    vi.mocked(require('@/api/auth').sendEmailCode).mockImplementation(mockSendEmailCode)

    render(<RegisterForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    // 填写表单并提交
    fireEvent.change(screen.getByLabelText('租户编码'), { target: { value: 'test-tenant' } })
    fireEvent.change(screen.getByLabelText('用户名'), { target: { value: 'testuser' } })
    fireEvent.change(screen.getByLabelText('邮箱地址'), { target: { value: '<EMAIL>' } })
    fireEvent.change(screen.getByLabelText('密码'), { target: { value: 'Password123!' } })
    
    fireEvent.click(screen.getByRole('button', { name: '获取验证码' }))
    
    await waitFor(() => {
      expect(mockSendEmailCode).toHaveBeenCalled()
    })
    
    fireEvent.change(screen.getByLabelText('邮箱验证码'), { target: { value: '123456' } })
    fireEvent.click(screen.getByLabelText(/我已阅读并同意/))
    fireEvent.click(screen.getByRole('button', { name: '注册账户' }))

    expect(screen.getByText('注册中...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '注册中...' })).toBeDisabled()
  })
})
