/**
 * @file 登录表单组件测试
 * @description 测试登录表单的功能和交互
 * @status 开发中
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { vi, describe, it, expect, beforeEach } from 'vitest'
import { LoginForm } from '../LoginForm'

// Mock zustand store
vi.mock('@/stores/authStore', () => ({
  useAuth: () => ({
    login: vi.fn(),
    isLoading: false,
    error: null,
    clearError: vi.fn()
  })
}))

// Mock utils
vi.mock('@/utils/common', () => ({
  cn: (...classes: string[]) => classes.filter(Boolean).join(' ')
}))

describe('LoginForm', () => {
  const mockOnSuccess = vi.fn()
  const mockOnError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders login form correctly', () => {
    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    expect(screen.getByText('登录')).toBeInTheDocument()
    expect(screen.getByText('请输入您的邮箱和密码')).toBeInTheDocument()
    expect(screen.getByLabelText('租户ID')).toBeInTheDocument()
    expect(screen.getByLabelText('邮箱地址')).toBeInTheDocument()
    expect(screen.getByLabelText('密码')).toBeInTheDocument()
    expect(screen.getByLabelText('记住我')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录' })).toBeInTheDocument()
  })

  it('shows validation errors for empty fields', async () => {
    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const submitButton = screen.getByRole('button', { name: '登录' })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(screen.getByText('此字段为必填项')).toBeInTheDocument()
    })
  })

  it('validates email format', async () => {
    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const emailInput = screen.getByLabelText('邮箱地址')
    const tenantInput = screen.getByLabelText('租户ID')
    const passwordInput = screen.getByLabelText('密码')
    
    fireEvent.change(tenantInput, { target: { value: 'test-tenant' } })
    fireEvent.change(emailInput, { target: { value: 'invalid-email' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.blur(emailInput)

    await waitFor(() => {
      expect(screen.getByText('请输入有效的邮箱地址')).toBeInTheDocument()
    })
  })

  it('validates password length', async () => {
    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const passwordInput = screen.getByLabelText('密码')
    
    fireEvent.change(passwordInput, { target: { value: '123' } })
    fireEvent.blur(passwordInput)

    await waitFor(() => {
      expect(screen.getByText('密码至少需要6个字符')).toBeInTheDocument()
    })
  })

  it('submits form with valid data', async () => {
    const mockLogin = vi.fn().mockResolvedValue({})
    
    vi.mocked(require('@/stores/authStore').useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
      clearError: vi.fn()
    })

    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const tenantInput = screen.getByLabelText('租户ID')
    const emailInput = screen.getByLabelText('邮箱地址')
    const passwordInput = screen.getByLabelText('密码')
    const submitButton = screen.getByRole('button', { name: '登录' })
    
    fireEvent.change(tenantInput, { target: { value: 'test-tenant' } })
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        tenant_id: 'test-tenant',
        login_type: 'email',
        identifier: '<EMAIL>',
        credential: 'password123',
        remember_me: false
      })
    })
  })

  it('handles login error', async () => {
    const mockLogin = vi.fn().mockRejectedValue(new Error('登录失败'))
    
    vi.mocked(require('@/stores/authStore').useAuth).mockReturnValue({
      login: mockLogin,
      isLoading: false,
      error: null,
      clearError: vi.fn()
    })

    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const tenantInput = screen.getByLabelText('租户ID')
    const emailInput = screen.getByLabelText('邮箱地址')
    const passwordInput = screen.getByLabelText('密码')
    const submitButton = screen.getByRole('button', { name: '登录' })
    
    fireEvent.change(tenantInput, { target: { value: 'test-tenant' } })
    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } })
    fireEvent.change(passwordInput, { target: { value: 'password123' } })
    fireEvent.click(submitButton)

    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith('登录失败')
    })
  })

  it('shows loading state during login', () => {
    vi.mocked(require('@/stores/authStore').useAuth).mockReturnValue({
      login: vi.fn(),
      isLoading: true,
      error: null,
      clearError: vi.fn()
    })

    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    expect(screen.getByText('登录中...')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: '登录中...' })).toBeDisabled()
  })

  it('displays error message', () => {
    vi.mocked(require('@/stores/authStore').useAuth).mockReturnValue({
      login: vi.fn(),
      isLoading: false,
      error: '用户名或密码错误',
      clearError: vi.fn()
    })

    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    expect(screen.getByText('用户名或密码错误')).toBeInTheDocument()
  })

  it('toggles remember me checkbox', () => {
    render(<LoginForm onSuccess={mockOnSuccess} onError={mockOnError} />)
    
    const checkbox = screen.getByLabelText('记住我') as HTMLInputElement
    
    expect(checkbox.checked).toBe(false)
    
    fireEvent.click(checkbox)
    expect(checkbox.checked).toBe(true)
    
    fireEvent.click(checkbox)
    expect(checkbox.checked).toBe(false)
  })
})
