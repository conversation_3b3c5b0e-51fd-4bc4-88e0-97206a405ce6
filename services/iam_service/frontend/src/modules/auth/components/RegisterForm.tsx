/**
 * @file 注册表单组件
 * @description 用户注册表单，支持邮箱验证码注册
 * @status 开发中
 */

import React, { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { validateForm, registerFormRules, sendCodeFormRules } from '@/utils/validation'
import { RegisterRequest, SendEmailCodeRequest } from '@/api/types'
import * as authApi from '@/api/auth'

export interface RegisterFormProps {
  onSuccess?: (data: any) => void
  onError?: (error: string) => void
  className?: string
}

export const RegisterForm: React.FC<RegisterFormProps> = ({
  onSuccess,
  onError,
  className
}) => {
  // 表单数据
  const [formData, setFormData] = useState<RegisterRequest>({
    tenant_code: '',
    username: '',
    email: '',
    phone: '',
    nickname: '',
    password: '',
    verification_code: '',
    code_id: '',
    agree_terms: false
  })
  
  // 表单状态
  const [isLoading, setIsLoading] = useState(false)
  const [isSendingCode, setIsSendingCode] = useState(false)
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({})
  const [touched, setTouched] = useState<Record<string, boolean>>({})
  const [error, setError] = useState<string | null>(null)
  
  // 验证码状态
  const [codeCountdown, setCodeCountdown] = useState(0)
  const [codeSent, setCodeSent] = useState(false)

  // 处理输入变化
  const handleInputChange = (field: keyof RegisterRequest) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const value = e.target.type === 'checkbox' ? e.target.checked : e.target.value
    
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // 清除该字段的验证错误
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }

    // 清除全局错误
    if (error) {
      setError(null)
    }
  }

  // 处理字段失焦
  const handleBlur = (field: keyof RegisterRequest) => () => {
    setTouched(prev => ({
      ...prev,
      [field]: true
    }))

    // 验证单个字段
    const result = validateForm({ [field]: formData[field] }, { [field]: registerFormRules[field] })
    if (result.errors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: result.errors[field]
      }))
    }
  }

  // 发送验证码
  const handleSendCode = async () => {
    // 验证邮箱和租户编码
    const validation = validateForm(
      {
        email: formData.email,
        tenant_code: formData.tenant_code
      },
      {
        email: sendCodeFormRules.email,
        tenant_code: registerFormRules.tenant_code
      }
    )

    if (!validation.isValid) {
      setValidationErrors(prev => ({
        ...prev,
        ...validation.errors
      }))
      setTouched(prev => ({
        ...prev,
        email: true,
        tenant_code: true
      }))
      return
    }

    setIsSendingCode(true)
    setError(null)

    try {
      // 先获取租户信息
      const tenantInfo = await authApi.getTenantByCode(formData.tenant_code)

      const sendCodeData: SendEmailCodeRequest = {
        tenant_id: tenantInfo.tenant_id,
        email: formData.email,
        scene: 'register'
      }

      const response = await authApi.sendEmailCode(sendCodeData)

      setFormData(prev => ({
        ...prev,
        code_id: response.code_id
      }))

      setCodeSent(true)
      setCodeCountdown(60)

      // 开始倒计时
      const timer = setInterval(() => {
        setCodeCountdown(prev => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)

    } catch (err: any) {
      setError(err.message || '发送验证码失败')
      onError?.(err.message || '发送验证码失败')
    } finally {
      setIsSendingCode(false)
    }
  }

  // 处理表单提交
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // 验证表单
    const validation = validateForm(formData, registerFormRules)
    
    if (!validation.isValid) {
      setValidationErrors(validation.errors)
      setTouched({
        tenant_code: true,
        username: true,
        email: true,
        password: true,
        verification_code: true,
        agree_terms: true
      })
      return
    }

    // 检查是否已发送验证码
    if (!formData.code_id) {
      setError('请先获取邮箱验证码')
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await authApi.register(formData)
      onSuccess?.(response)
    } catch (err: any) {
      const errorMessage = err.message || '注册失败，请重试'
      setError(errorMessage)
      onError?.(errorMessage)
    } finally {
      setIsLoading(false)
    }
  }

  // 获取字段错误信息
  const getFieldError = (field: keyof RegisterRequest) => {
    return touched[field] ? validationErrors[field] : ''
  }

  return (
    <Card className={className}>
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-bold text-gray-900">
          注册账户
        </CardTitle>
        <p className="text-gray-600 mt-2">
          创建您的新账户
        </p>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 租户编码 */}
          <Input
            label="租户编码"
            type="text"
            placeholder="请输入租户编码"
            value={formData.tenant_code}
            onChange={handleInputChange('tenant_code')}
            onBlur={handleBlur('tenant_code')}
            error={getFieldError('tenant_code')}
            disabled={isLoading}
            required
          />

          {/* 用户名 */}
          <Input
            label="用户名"
            type="text"
            placeholder="请输入用户名"
            value={formData.username}
            onChange={handleInputChange('username')}
            onBlur={handleBlur('username')}
            error={getFieldError('username')}
            disabled={isLoading}
            required
            leftIcon={
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            }
          />

          {/* 邮箱地址 */}
          <div className="space-y-2">
            <Input
              label="邮箱地址"
              type="email"
              placeholder="请输入邮箱地址"
              value={formData.email}
              onChange={handleInputChange('email')}
              onBlur={handleBlur('email')}
              error={getFieldError('email')}
              disabled={isLoading}
              required
              leftIcon={
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              }
            />
          </div>

          {/* 手机号（可选） */}
          <Input
            label="手机号"
            type="tel"
            placeholder="请输入手机号（可选）"
            value={formData.phone}
            onChange={handleInputChange('phone')}
            onBlur={handleBlur('phone')}
            error={getFieldError('phone')}
            disabled={isLoading}
            leftIcon={
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
              </svg>
            }
          />

          {/* 昵称（可选） */}
          <Input
            label="昵称"
            type="text"
            placeholder="请输入昵称（可选）"
            value={formData.nickname}
            onChange={handleInputChange('nickname')}
            onBlur={handleBlur('nickname')}
            error={getFieldError('nickname')}
            disabled={isLoading}
          />

          {/* 密码 */}
          <Input
            label="密码"
            type="password"
            placeholder="请输入密码"
            value={formData.password}
            onChange={handleInputChange('password')}
            onBlur={handleBlur('password')}
            error={getFieldError('password')}
            disabled={isLoading}
            required
            leftIcon={
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 0h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            }
          />

          {/* 邮箱验证码 */}
          <div className="flex space-x-2">
            <div className="flex-1">
              <Input
                label="邮箱验证码"
                type="text"
                placeholder="请输入验证码"
                value={formData.verification_code}
                onChange={handleInputChange('verification_code')}
                onBlur={handleBlur('verification_code')}
                error={getFieldError('verification_code')}
                disabled={isLoading}
                required
              />
            </div>
            <div className="flex items-end">
              <Button
                type="button"
                variant="outline"
                size="md"
                onClick={handleSendCode}
                disabled={isSendingCode || codeCountdown > 0 || !formData.email || !formData.tenant_code}
                loading={isSendingCode}
                className="whitespace-nowrap"
              >
                {codeCountdown > 0 ? `${codeCountdown}s` : codeSent ? '重新发送' : '获取验证码'}
              </Button>
            </div>
          </div>

          {/* 服务条款同意 */}
          <div className="flex items-start">
            <input
              id="agree-terms"
              type="checkbox"
              checked={formData.agree_terms}
              onChange={handleInputChange('agree_terms')}
              disabled={isLoading}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-1"
            />
            <label htmlFor="agree-terms" className="ml-2 block text-sm text-gray-700">
              我已阅读并同意{' '}
              <a href="#" className="text-blue-600 hover:text-blue-500 hover:underline">
                服务条款
              </a>
              {' '}和{' '}
              <a href="#" className="text-blue-600 hover:text-blue-500 hover:underline">
                隐私政策
              </a>
            </label>
          </div>
          {getFieldError('agree_terms') && (
            <p className="text-sm text-red-600">{getFieldError('agree_terms')}</p>
          )}

          {/* 全局错误信息 */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex">
                <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div className="ml-3">
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 提交按钮 */}
          <Button
            type="submit"
            variant="primary"
            size="lg"
            loading={isLoading}
            disabled={isLoading}
            className="w-full"
          >
            {isLoading ? '注册中...' : '注册账户'}
          </Button>
        </form>

        {/* 其他操作链接 */}
        <div className="mt-6 text-center">
          <div className="text-sm text-gray-600">
            已有账户？{' '}
            <a
              href="#"
              className="text-blue-600 hover:text-blue-500 hover:underline"
              onClick={(e) => {
                e.preventDefault()
                // TODO: 导航到登录页面
                window.location.href = '/login'
              }}
            >
              立即登录
            </a>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
