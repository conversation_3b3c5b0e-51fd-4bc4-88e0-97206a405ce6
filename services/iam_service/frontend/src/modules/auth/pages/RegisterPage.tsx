/**
 * @file 注册页面
 * @description 用户注册页面，包含注册表单和页面布局
 * @status 开发中
 */

import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { RegisterForm } from '../components/RegisterForm'
import { RegisterResponse } from '@/api/types'

export const RegisterPage: React.FC = () => {
  const navigate = useNavigate()
  const [registrationResult, setRegistrationResult] = useState<RegisterResponse | null>(null)

  // 注册成功处理
  const handleRegisterSuccess = (data: RegisterResponse) => {
    setRegistrationResult(data)
    // 注册成功后显示激活提示，不自动跳转
  }

  // 注册错误处理
  const handleRegisterError = (error: string) => {
    console.error('Register error:', error)
    // 错误已经在RegisterForm中显示，这里可以添加额外的错误处理逻辑
  }

  // 如果注册成功，显示激活提示页面
  if (registrationResult) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        {/* 页面头部 */}
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center">
            {/* Logo */}
            <div className="h-12 w-12 bg-green-600 rounded-lg flex items-center justify-center">
              <svg
                className="h-8 w-8 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>
          
          <h1 className="mt-6 text-center text-3xl font-bold text-gray-900">
            注册成功！
          </h1>
          <p className="mt-2 text-center text-sm text-gray-600">
            请查收激活邮件
          </p>
        </div>

        {/* 成功信息 */}
        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            <div className="text-center space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-md p-4">
                <div className="flex">
                  <svg className="h-5 w-5 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">
                      账户创建成功
                    </h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p>您的账户已成功创建，激活邮件已发送至：</p>
                      <p className="font-medium mt-1">{registrationResult.email}</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-3 text-sm text-gray-600">
                <p>
                  <strong>用户名：</strong>{registrationResult.username}
                </p>
                <p>
                  <strong>注册时间：</strong>{new Date(registrationResult.created_at).toLocaleString()}
                </p>
                <p>
                  <strong>账户状态：</strong>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 ml-1">
                    待激活
                  </span>
                </p>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
                <div className="text-sm text-blue-700">
                  <h4 className="font-medium mb-2">接下来的步骤：</h4>
                  <ol className="list-decimal list-inside space-y-1 text-left">
                    <li>检查您的邮箱收件箱</li>
                    <li>点击邮件中的激活链接</li>
                    <li>激活成功后即可登录使用</li>
                  </ol>
                </div>
              </div>

              <div className="space-y-3">
                <button
                  onClick={() => navigate('/login')}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  前往登录页面
                </button>
                
                <button
                  onClick={() => setRegistrationResult(null)}
                  className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  重新注册
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* 页面底部 */}
        <div className="mt-8 text-center">
          <div className="text-sm text-gray-500">
            <p>没有收到邮件？请检查垃圾邮件文件夹</p>
            <p className="mt-1">或联系管理员获取帮助</p>
          </div>
        </div>
      </div>
    )
  }

  // 显示注册表单
  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      {/* 页面头部 */}
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="flex justify-center">
          {/* Logo */}
          <div className="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
            <svg
              className="h-8 w-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"
              />
            </svg>
          </div>
        </div>
        
        <h1 className="mt-6 text-center text-3xl font-bold text-gray-900">
          IAM 管理系统
        </h1>
        <p className="mt-2 text-center text-sm text-gray-600">
          身份与访问管理平台
        </p>
      </div>

      {/* 注册表单 */}
      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <RegisterForm
          onSuccess={handleRegisterSuccess}
          onError={handleRegisterError}
          className="shadow-lg"
        />
      </div>

      {/* 页面底部 */}
      <div className="mt-8 text-center">
        <div className="text-sm text-gray-500">
          <p>© 2024 IAM 管理系统. 保留所有权利.</p>
        </div>
        
        {/* 开发环境提示 */}
        {import.meta.env.DEV && (
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md mx-auto max-w-md">
            <div className="flex">
              <svg className="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-yellow-700">
                  <strong>开发模式</strong>
                </p>
                <p className="text-xs text-yellow-600 mt-1">
                  当前运行在开发环境中
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
