/**
 * @file 认证模块
 * @description 用户认证相关功能模块，包含登录、注册、密码管理等功能
 * @status 开发中
 */

import React from 'react'
import { RouteObject } from 'react-router-dom'
import { LoginPage } from './pages/LoginPage'
import { RegisterPage } from './pages/RegisterPage'
import { ActivatePage } from './pages/ActivatePage'

// 模块配置
export const authModuleConfig = {
  id: 'auth',
  name: '用户认证',
  version: '1.0.0',
  description: '用户登录、注册和认证管理',
  enabled: true,
  lazy: false,
  dependencies: [],
  permissions: []
}

// 认证模块路由
export const authRoutes: RouteObject[] = [
  {
    path: '/login',
    element: <LoginPage />
  },
  {
    path: '/register',
    element: <RegisterPage />
  },
  {
    path: '/activate',
    element: <ActivatePage />
  }
  // 后续可以添加更多认证相关路由
  // {
  //   path: '/forgot-password',
  //   element: <ForgotPasswordPage />
  // },
  // {
  //   path: '/reset-password',
  //   element: <ResetPasswordPage />
  // }
]

// 认证模块定义
export const authModule = {
  config: authModuleConfig,
  routes: authRoutes,
  initialize: async () => {
    console.log('Auth module initialized')
  },
  destroy: async () => {
    console.log('Auth module destroyed')
  }
}

// 导出组件
export { LoginPage, RegisterPage, ActivatePage }
export * from './components/LoginForm'
export * from './components/RegisterForm'
